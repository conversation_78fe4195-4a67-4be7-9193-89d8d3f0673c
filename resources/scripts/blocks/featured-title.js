import { registerBlockType } from '@wordpress/blocks';
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { SelectControl } from '@wordpress/components';
import { commonAttributes } from './common';
import {
    TextControl,
    ButtonEditor,
    ImageUploader
} from './editor';

registerBlockType('sage/featured-title', {
    apiVersion: 2,
    title: 'Featured Title',
    icon: 'editor-bold',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        size: {
            type: 'string',
            default: 'large'
        },
        backgroundImage: {
            type: 'string',
            default: ''
        },
        backgroundSize: {
            type: 'string',
            default: 'cover'
        },
        backgroundPosition: {
            type: 'string',
            default: 'center'
        }
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, buttonText, buttonLink, size, backgroundImage, backgroundSize, backgroundPosition } = attributes;
        const blockProps = useBlockProps();
        const hasButton = !!buttonText;

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Featured Title</h2>
                    </div>
                <TextControl
                    label="Title"
                    value={title}
                    onChange={(value) => setAttributes({ title: value })}
                    placeholder="Enter featured title..."
                    tagName="h2"
                    allowedFormats={['core/bold']}
                />

                <ButtonEditor
                    buttonText={buttonText}
                    buttonLink={buttonLink}
                    onChangeText={(value) => setAttributes({ buttonText: value })}
                    onChangeLink={(value) => setAttributes({ buttonLink: value })}
                    label="Button"
                />

                <div className="mb-4">
                    <label className="block text-sm font-medium text-neutral-70 mb-2">Size</label>
                    <div className="p-3 bg-white rounded-md border border-neutral-30">
                        <SelectControl
                            value={size}
                            options={[
                                { label: 'Large', value: 'large' },
                                { label: 'Small', value: 'small' },
                            ]}
                            onChange={(value) => setAttributes({ size: value })}
                        />
                    </div>
                    <p className="mt-1 text-xs text-neutral-60">Select size (only affects appearance when no button is present)</p>
                </div>

                {!hasButton && size === 'small' && (
                    <>
                        <ImageUploader
                            image={backgroundImage}
                            onSelect={(url) => setAttributes({ backgroundImage: url })}
                            onRemove={() => setAttributes({ backgroundImage: '' })}
                            label="Background Image"
                            description="Upload a background image for the featured title section. Only available for small size when no button is present."
                            height={48}
                            objectFit="cover"
                            altText="Featured Title Background"
                        />

                        {backgroundImage && (
                            <>
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Background Size</label>
                                    <div className="p-3 bg-white rounded-md border border-neutral-30">
                                        <SelectControl
                                            value={backgroundSize}
                                            options={[
                                                { label: 'Cover', value: 'cover' },
                                                { label: 'Contain', value: 'contain' },
                                                { label: '100%', value: '100%' },
                                                { label: 'Auto', value: 'auto' },
                                            ]}
                                            onChange={(value) => setAttributes({ backgroundSize: value })}
                                        />
                                    </div>
                                    <p className="mt-1 text-xs text-neutral-60">Select how the background image should be sized</p>
                                </div>

                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Background Position</label>
                                    <div className="p-3 bg-white rounded-md border border-neutral-30">
                                        <SelectControl
                                            value={backgroundPosition}
                                            options={[
                                                { label: 'Center', value: 'center' },
                                                { label: 'Top', value: 'top' },
                                                { label: 'Right', value: 'right' },
                                                { label: 'Bottom', value: 'bottom' },
                                                { label: 'Left', value: 'left' },
                                                { label: 'Top Left', value: 'top left' },
                                                { label: 'Top Right', value: 'top right' },
                                                { label: 'Bottom Left', value: 'bottom left' },
                                                { label: 'Bottom Right', value: 'bottom right' },
                                            ]}
                                            onChange={(value) => setAttributes({ backgroundPosition: value })}
                                        />
                                    </div>
                                    <p className="mt-1 text-xs text-neutral-60">Select how the background image should be positioned</p>
                                </div>
                            </>
                        )}
                    </>
                )}
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="span" value={attributes.buttonText} />
                    <a href={attributes.buttonLink} />
                    {attributes.backgroundImage && (
                        <>
                            <img src={attributes.backgroundImage} alt="Background" />
                            <span data-background-size={attributes.backgroundSize} />
                            <span data-background-position={attributes.backgroundPosition} />
                        </>
                    )}
                </div>
                <div data-dynamic="sage/featured-title"></div>
            </div>
        );
    }
});