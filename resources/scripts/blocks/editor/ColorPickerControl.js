import { useState } from '@wordpress/element';
import { Button, ColorPicker, Popover } from '@wordpress/components';
const ColorPickerControl = ({ 
    color = '#000000', 
    onChange, 
    label = 'Color',
    disableAlpha = true
}) => {
    const [showColorPicker, setShowColorPicker] = useState(false);
    
    return (
        <div>
            <p className="mb-2 text-sm font-medium text-neutral-70">{label}</p>
            <div className="flex items-center gap-3">
                <div
                    className="w-10 h-10 rounded-lg cursor-pointer border border-neutral-30 shadow-sm"
                    style={{ backgroundColor: color || '#000000' }}
                    onClick={() => setShowColorPicker(true)}
                ></div>
                <div className="text-sm text-neutral-70">
                    {color || '#000000'}
                </div>
                <Button
                    variant="secondary"
                    className="text-sm py-1 px-2"
                    onClick={() => setShowColorPicker(true)}
                >
                    Change
                </Button>
            </div>
            {showColorPicker && (
                <Popover onClose={() => setShowColorPicker(false)} className="mt-2">
                    <div className="p-3">
                        <ColorPicker
                            color={color || '#000000'}
                            onChangeComplete={(value) => onChange(value.hex)}
                            disableAlpha={disableAlpha}
                        />
                    </div>
                </Popover>
            )}
        </div>
    );
};

export default ColorPickerControl;
