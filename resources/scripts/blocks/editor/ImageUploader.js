import React from 'react';
import { MediaUpload } from '@wordpress/block-editor';
import { Button } from '@wordpress/components';
const ImageUploader = ({ 
    image, 
    onSelect, 
    onRemove = () => onSelect(''),
    label = 'Image',
    description = 'Upload an image.',
    placeholderText = 'Upload Image',
    altText = 'Uploaded Image',
    objectFit = 'cover',
    height = 48
}) => {
    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-70 mb-2">{label}</label>
            <div className="relative group">
                <MediaUpload
                    onSelect={(media) => onSelect(media.url)}
                    allowedTypes={['image']}
                    value={image}
                    render={({ open }) => (
                        <div
                            onClick={open}
                            className={`flex items-center justify-center w-full h-${height} rounded-lg overflow-hidden cursor-pointer ${image ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                            style={{ height: `${height * 4}px` }}
                        >
                            {image ? (
                                <>
                                    <img
                                        className={`w-full h-full object-${objectFit}`}
                                        src={image}
                                        alt={altText}
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                        <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Image</span>
                                    </div>
                                </>
                            ) : (
                                <div className="text-center">
                                    <div className="text-neutral-60 text-3xl mb-2">+</div>
                                    <span className="text-neutral-60 text-sm">{placeholderText}</span>
                                </div>
                            )}
                        </div>
                    )}
                />
                {image && (
                    <Button
                        isDestructive
                        className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                        onClick={onRemove}
                        icon="no-alt"
                    />
                )}
            </div>
            <p className="mt-2 text-xs text-neutral-60">{description}</p>
        </div>
    );
};

export default ImageUploader;
