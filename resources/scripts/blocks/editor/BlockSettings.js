import { SectionHeader } from './';
import TextControl from './TextControl';
import ImageUploader from './ImageUploader';
import ColorPickerControl from './ColorPickerControl';
import { useState, useEffect } from '@wordpress/element';
import { Button, Popover, SearchControl } from '@wordpress/components';
const BlockSettings = ({
    title,
    onTitleChange,
    description,
    onDescriptionChange,
    icon,
    onIconSelect,
    onIconRemove,
    accentColor,
    onAccentColorChange
}) => {
    const [availableCaseStudyIcons, setAvailableCaseStudyIcons] = useState([]);
    const [showIconSelector, setShowIconSelector] = useState(false);
    const [iconSearchTerm, setIconSearchTerm] = useState('');


    useEffect(() => {
        if (window.caseStudyIconsData && window.caseStudyIconsData.icons) {
            console.log('Case Study Icons found:', window.caseStudyIconsData.icons);
            setAvailableCaseStudyIcons(window.caseStudyIconsData.icons);
        } else {
            console.log('No case study icons data found in window object');


            fetch('/wp-json/wp/v2/settings/case_study_icons')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Fetched case study icons:', data);
                    if (data && Array.isArray(data)) {
                        setAvailableCaseStudyIcons(data);
                    }
                })
                .catch(error => {
                    console.error('Error fetching case study icons:', error);
                });
        }
    }, []);

    const selectCaseStudyIcon = (icon) => {
        onIconSelect(icon.url);
        onAccentColorChange(icon.color);
        setShowIconSelector(false);
    };
    return (
        <>
            <SectionHeader title="Block Settings" />
            <TextControl
                label="Block Title"
                value={title}
                onChange={onTitleChange}
                placeholder="Enter title"
                tagName="h2"
            />

            {description !== undefined && onDescriptionChange && (
                <TextControl
                    label="Block Description"
                    value={description}
                    onChange={onDescriptionChange}
                    placeholder="Enter description"
                    tagName="p"
                    multiline={true}
                />
            )}

            <div className="flex flex-wrap gap-6 items-center mb-6">
                <div className="flex-shrink-0">
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-neutral-70 mb-2">Block Icon</label>
                        <div className="flex flex-col gap-3 relative">

                            {icon && (
                                <div className="relative group inline-block">
                                    <div className="flex items-center gap-3">
                                        <div
                                            className="w-[42px] h-[42px] rounded-full flex items-center justify-center"
                                            style={{ backgroundColor: accentColor || '#538564' }}
                                        >
                                            <img
                                                src={icon}
                                                alt="Block Icon"
                                                className="w-5 h-5 object-contain"
                                            />
                                        </div>
                                        <Button
                                            isDestructive
                                            variant="tertiary"
                                            onClick={onIconRemove}
                                            className="text-sm"
                                        >
                                            Remove
                                        </Button>
                                    </div>
                                </div>
                            )}


                            <div className="flex flex-wrap gap-2">

                                <Button
                                    variant="secondary"
                                    onClick={() => setShowIconSelector(true)}
                                    className="text-sm"
                                >
                                    {icon ? 'Change Icon' : 'Select Icon'}
                                </Button>


                                <Button
                                    variant="secondary"
                                    onClick={() => {

                                        const input = document.createElement('input');
                                        input.type = 'file';
                                        input.accept = 'image/*';
                                        input.onchange = (e) => {
                                            const file = e.target.files[0];
                                            if (file) {

                                                const mediaUploader = wp.media({
                                                    title: 'Select or Upload Icon',
                                                    button: {
                                                        text: 'Use this icon'
                                                    },
                                                    multiple: false
                                                });

                                                mediaUploader.on('select', function() {
                                                    const attachment = mediaUploader.state().get('selection').first().toJSON();
                                                    onIconSelect(attachment.url);

                                                    if (!accentColor) {
                                                        onAccentColorChange('#538564');
                                                    }
                                                });

                                                mediaUploader.open();
                                            }
                                        };
                                        input.click();
                                    }}
                                    className="text-sm"
                                >
                                    Custom Upload
                                </Button>
                            </div>


                            {showIconSelector && availableCaseStudyIcons.length > 0 ? (
                                <div className="icon-selector-container absolute left-[calc(100%_-_49px)] -bottom-10">
                                    <Popover
                                        onClose={() => setShowIconSelector(false)}
                                        className="case-study-icons-popover mt-[-12px]"
                                        noArrow={true}
                                        position="bottom center"
                                    >
                                        <div className="p-4 w-[350px]">
                                            <h3 className="text-base font-medium mb-3">Select an Icon</h3>

                                            <SearchControl
                                                value={iconSearchTerm}
                                                onChange={setIconSearchTerm}
                                                className="mb-3"
                                                placeholder="Search icons..."
                                            />

                                            <div className="case-study-icons-grid max-h-[300px] overflow-y-auto p-2 bg-white rounded-lg border border-neutral-30" style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px' }}>
                                                {availableCaseStudyIcons
                                                    .filter(icon => icon.name.toLowerCase().includes(iconSearchTerm.toLowerCase()))
                                                    .map((caseStudyIcon, index) => (
                                                        <div
                                                            key={index}
                                                            className="case-study-icon-item p-2 rounded-lg cursor-pointer hover:bg-neutral-10 transition-all"
                                                            onClick={() => selectCaseStudyIcon(caseStudyIcon)}
                                                        >
                                                            <div className="flex flex-col items-center gap-1">
                                                                <div
                                                                    className="w-[42px] h-[42px] rounded-full flex items-center justify-center mb-1"
                                                                    style={{ backgroundColor: caseStudyIcon.color }}
                                                                >
                                                                    <img
                                                                        src={caseStudyIcon.url}
                                                                        alt={caseStudyIcon.name}
                                                                        className="w-5 h-5 object-contain"
                                                                    />
                                                                </div>
                                                                <span className="text-xs text-center">{caseStudyIcon.name}</span>
                                                            </div>
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                        </div>
                                    </Popover>
                                </div>
                            ) : null}
                        </div>
                        <p className="mt-2 text-xs text-neutral-60">Select an icon for this block.</p>
                    </div>
                </div>

                <div className="flex-grow">
                    <ColorPickerControl
                        color={accentColor}
                        onChange={onAccentColorChange}
                        label="Accent Color"
                    />
                </div>
            </div>
        </>
    );
};

export default BlockSettings;
