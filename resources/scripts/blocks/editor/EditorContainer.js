import React from 'react';
const EditorContainer = ({ children, blockTitle, ...blockProps }) => {
    return (
        <div {...blockProps} className={`${blockProps.className || ''} font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30`}>
            {blockTitle && (
                <div className="mb-4 border-b border-neutral-30">
                    <h2 className="text-2xl font-bold text-neutral-90">{blockTitle}</h2>
                </div>
            )}
            {children}
        </div>
    );
};

export default EditorContainer;
