import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    Button,
    Panel,
    PanelBody,
    SearchControl,
    SelectControl,
} from '@wordpress/components';
import { useState, useEffect } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';
const DEFAULT_ITEM = {
    title: '',
    subtitle: '',
    description: '',
    image: '',
    techIcons: [],
};
const DEFAULT_TAGLINE_ICON = {
    image: '',
    text: '',
};

registerBlockType('sage/text-image-collection', {
    apiVersion: 2,
    title: 'Text Image Collection',
    icon: 'grid-view',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        variant: {
            type: 'string',
            default: 'default',
        },
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        taglineIcons: {
            type: 'array',
            default: [],
        },
        items: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { variant, title, description, taglineIcons, items } = attributes;
        const [activeTaglineIcon, setActiveTaglineIcon] = useState(0);
        const [activeItem, setActiveItem] = useState(0);
        const [availableTechIcons, setAvailableTechIcons] = useState([]);
        const [iconSearchTerm, setIconSearchTerm] = useState('');
        const [showAllIcons, setShowAllIcons] = useState(false);
        useEffect(() => {
            if (window.technologyIconsData && window.technologyIconsData.icons) {
                console.log('Icons found in window object:', window.technologyIconsData.icons);
                setAvailableTechIcons(window.technologyIconsData.icons);
            } else {
                console.log('No technology icons data found in window object');
                fetch('/wp-json/wp/v2/settings/technology_icons')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Fetched technology icons:', data);
                        if (data && Array.isArray(data)) {
                            setAvailableTechIcons(data);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching technology icons:', error);
                    });
            }
        }, []);
        const addTaglineIcon = () => {
            const newTaglineIcons = [...taglineIcons, { ...DEFAULT_TAGLINE_ICON }];
            setAttributes({ taglineIcons: newTaglineIcons });
            setActiveTaglineIcon(newTaglineIcons.length - 1);
        };

        const removeTaglineIcon = (index) => {
            const newTaglineIcons = [...taglineIcons];
            newTaglineIcons.splice(index, 1);
            setAttributes({ taglineIcons: newTaglineIcons });
            setActiveTaglineIcon(Math.min(activeTaglineIcon, newTaglineIcons.length - 1));
        };

        const handleTaglineIconChange = (index, field, value) => {
            const newTaglineIcons = [...taglineIcons];
            newTaglineIcons[index] = { ...newTaglineIcons[index], [field]: value };
            setAttributes({ taglineIcons: newTaglineIcons });
        };
        const addItem = () => {
            const newItems = [...items, { ...DEFAULT_ITEM }];
            setAttributes({ items: newItems });
            setActiveItem(newItems.length - 1);
        };

        const removeItem = (index) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ items: newItems });
            setActiveItem(Math.min(activeItem, newItems.length - 1));
        };

        const handleItemChange = (index, field, value) => {
            const newItems = [...items];
            newItems[index] = { ...newItems[index], [field]: value };
            setAttributes({ items: newItems });
        };
        const toggleTechIcon = (itemIndex, iconId) => {
            const newItems = [...items];
            if (!newItems[itemIndex].techIcons) {
                newItems[itemIndex].techIcons = [];
            }

            const iconIndex = newItems[itemIndex].techIcons.indexOf(iconId);

            if (iconIndex === -1) {
                newItems[itemIndex].techIcons.push(iconId);
            } else {
                newItems[itemIndex].techIcons.splice(iconIndex, 1);
            }

            setAttributes({ items: newItems });
        };

        const isTechIconSelected = (itemIndex, iconId) => {
            return items[itemIndex]?.techIcons?.includes(iconId) || false;
        };
        if (items.length === 0) {
            addItem();
        }
        const filteredIcons = iconSearchTerm
            ? availableTechIcons.filter(icon =>
                icon.name.toLowerCase().includes(iconSearchTerm.toLowerCase()))
            : availableTechIcons;
        const displayIcons = showAllIcons ? filteredIcons : filteredIcons.slice(0, 12);

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Text Image Collection">
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-neutral-70 mb-2">Block Variant</label>
                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                            <SelectControl
                                value={variant}
                                options={[
                                    { label: 'Default (with icons)', value: 'default' },
                                    { label: 'No Icons', value: 'no-icons' },
                                ]}
                                onChange={(value) => setAttributes({ variant: value })}
                            />
                        </div>
                        <p className="mt-1 text-xs text-neutral-60">
                            {variant === 'no-icons'
                                ? 'No icons variant starts with image on right, text on left and centers text content'
                                : 'Default variant includes technology icons and alternates layout starting with image on left'
                            }
                        </p>
                    </div>

                    <TextControl
                        label="Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter title"
                        tagName="h2"
                    />

                    <TextControl
                        label="Description"
                        value={description}
                        onChange={(value) => setAttributes({ description: value })}
                        placeholder="Enter description"
                        tagName="p"
                    />
                    <Panel className="mb-4">
                        <PanelBody
                            title="Tagline Icons"
                            initialOpen={true}
                        >
                            <div className="mb-4">
                                <TabNavigation
                                    items={taglineIcons}
                                    activeTab={activeTaglineIcon}
                                    onTabChange={setActiveTaglineIcon}
                                    onAddItem={addTaglineIcon}
                                    onRemoveItem={() => removeTaglineIcon(activeTaglineIcon)}
                                    getItemTitle={(_, index) => `Icon ${index + 1}`}
                                    itemName="Icon"
                                    addButtonTitle="Add Icon"
                                />

                                {taglineIcons.length > 0 && (
                                    <ItemEditor
                                        itemIndex={activeTaglineIcon}
                                        onRemove={() => removeTaglineIcon(activeTaglineIcon)}
                                        itemName="Tagline Icon"
                                        showRemoveButton={taglineIcons.length > 1}
                                    >
                                        <ImageUploader
                                            image={taglineIcons[activeTaglineIcon].image}
                                            onSelect={(url) => handleTaglineIconChange(activeTaglineIcon, 'image', url)}
                                            onRemove={() => handleTaglineIconChange(activeTaglineIcon, 'image', '')}
                                            label="Icon Image"
                                            description="Upload an icon image."
                                            height={24}
                                            objectFit="contain"
                                            altText="Tagline Icon"
                                        />

                                        <TextControl
                                            label="Icon Text"
                                            value={taglineIcons[activeTaglineIcon].text}
                                            onChange={(value) => handleTaglineIconChange(activeTaglineIcon, 'text', value)}
                                            placeholder="Enter icon text"
                                            tagName="p"
                                        />
                                    </ItemEditor>
                                )}
                            </div>
                        </PanelBody>
                    </Panel>
                    <Panel>
                        <PanelBody
                            title="Collection Items"
                            initialOpen={true}
                        >
                            <div className="mb-4">
                                <TabNavigation
                                    items={items}
                                    activeTab={activeItem}
                                    onTabChange={setActiveItem}
                                    onAddItem={addItem}
                                    onRemoveItem={() => removeItem(activeItem)}
                                    getItemTitle={(item, index) => item.title || `Item ${index + 1}`}
                                    itemName="Item"
                                    addButtonTitle="Add Item"
                                />

                                {items.length > 0 && (
                                    <ItemEditor
                                        itemIndex={activeItem}
                                        onRemove={() => removeItem(activeItem)}
                                        itemName="Item"
                                        showRemoveButton={items.length > 1}
                                    >
                                        <ImageUploader
                                            image={items[activeItem].image}
                                            onSelect={(url) => handleItemChange(activeItem, 'image', url)}
                                            onRemove={() => handleItemChange(activeItem, 'image', '')}
                                            label="Item Image"
                                            description="Upload an image for this item (min-height: 424px recommended)."
                                            height={60}
                                            objectFit="cover"
                                            altText="Item Image"
                                        />

                                        <TextControl
                                            label="Title"
                                            value={items[activeItem].title}
                                            onChange={(value) => handleItemChange(activeItem, 'title', value)}
                                            placeholder="Enter item title"
                                            tagName="h3"
                                        />

                                        <TextControl
                                            label="Subtitle"
                                            value={items[activeItem].subtitle}
                                            onChange={(value) => handleItemChange(activeItem, 'subtitle', value)}
                                            placeholder="Enter item subtitle"
                                            tagName="h4"
                                        />
                                        {variant === 'default' && (
                                        <div className="mb-4">
                                            <h4 className="text-sm font-medium text-neutral-70 mb-2">Technology Icons</h4>
                                            {items[activeItem].techIcons && items[activeItem].techIcons.length > 0 && (
                                                <div className="mb-4">
                                                    <h5 className="text-xs font-medium text-neutral-70 mb-2">Selected Icons</h5>
                                                    <div className="flex flex-wrap gap-2 p-3 bg-white rounded-lg border border-neutral-30">
                                                        {items[activeItem].techIcons.map((iconId, idx) => {
                                                            const icon = availableTechIcons.find(i => i.id === iconId);
                                                            if (!icon) return null;

                                                            return (
                                                                <div
                                                                    key={idx}
                                                                    className="relative group"
                                                                    title={icon.name}
                                                                >
                                                                    <div className="w-10 h-10 flex items-center justify-center p-1 bg-neutral-10 rounded-md border border-neutral-30">
                                                                        <img
                                                                            src={icon.url}
                                                                            alt={icon.name}
                                                                            className="max-w-full max-h-full object-contain"
                                                                        />
                                                                    </div>
                                                                    <button
                                                                        className="absolute -top-1 -right-1 w-5 h-5 bg-white rounded-full border border-red-300 text-red-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                                                        onClick={() => toggleTechIcon(activeItem, iconId)}
                                                                        aria-label={`Remove ${icon.name}`}
                                                                    >
                                                                        <span className="dashicons dashicons-no-alt" style={{ fontSize: '12px' }}></span>
                                                                    </button>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                </div>
                                            )}
                                            <div className="p-3 bg-white rounded-lg border border-neutral-30">
                                                <SearchControl
                                                    value={iconSearchTerm}
                                                    onChange={setIconSearchTerm}
                                                    className="mb-3"
                                                    placeholder="Search technology icons..."
                                                />

                                                <div className="technology-icons-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(70px, 1fr))', gap: '10px' }}>
                                                    {displayIcons.map((icon, idx) => {
                                                        const isSelected = isTechIconSelected(activeItem, icon.id);
                                                        return (
                                                            <div
                                                                key={idx}
                                                                className={`technology-icon-item relative group transition-all ${isSelected ? 'scale-105' : ''}`}
                                                                onClick={() => toggleTechIcon(activeItem, icon.id)}
                                                                title={icon.name}
                                                            >
                                                                <div className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all ${isSelected ? 'bg-primary-20 border border-primary-70' : 'bg-neutral-10 hover:bg-neutral-20 border border-transparent'}`}>
                                                                    <div className="w-10 h-10 flex items-center justify-center mb-1">
                                                                        <img
                                                                            src={icon.url}
                                                                            alt={icon.name}
                                                                            className="max-w-full max-h-full object-contain"
                                                                        />
                                                                    </div>
                                                                    <span className="text-xs text-center truncate w-full">{icon.name}</span>
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>

                                                {filteredIcons.length > 12 && !showAllIcons && (
                                                    <Button
                                                        variant="secondary"
                                                        className="mt-3 w-full justify-center"
                                                        onClick={() => setShowAllIcons(true)}
                                                    >
                                                        Show All Icons ({filteredIcons.length})
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                        )}

                                        <TextControl
                                            label="Description"
                                            value={items[activeItem].description}
                                            onChange={(value) => handleItemChange(activeItem, 'description', value)}
                                            placeholder="Enter item description"
                                            tagName="p"
                                        />
                                    </ItemEditor>
                                )}
                            </div>
                        </PanelBody>
                    </Panel>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
