import { registerBlockType } from '@wordpress/blocks';
import {
	RichText,
	useBlockProps,
} from '@wordpress/block-editor';
import {
	Button,
	ToggleControl,
	Panel,
	PanelBody
} from '@wordpress/components';
import { Fragment, useEffect } from '@wordpress/element';
import { commonAttributes } from './common';
import {
	EditorContainer,
	BlockSettings,
	TextControl
} from './editor';

registerBlockType('sage/case-study-outcomes', {
	apiVersion: 2,
	title: 'Case Study: Outcomes',
	icon: 'analytics',
	category: 'layout',
	attributes: {
		...commonAttributes,
		title: { type: 'string' },
		description: { type: 'string' },
		icon: { type: 'string' },
		accentColor: { type: 'string' },
		outcome: { type: 'string' },
		percentageValues: {
			type: 'array',
			default: [],
		},
		textOutcomes: {
			type: 'array',
			default: [],
		},
		outcomes: {
			type: 'array',
			default: [],
		},
	},
	edit: ({ attributes, setAttributes }) => {
		const blockProps = useBlockProps();
		const {
			title,
			description,
			icon,
			accentColor,
			outcome,
			percentageValues,
			textOutcomes,
			outcomes,
		} = attributes;
		const addOutcome = () => {
			const newItem = { text: '', showPercentage: false, percentage: 0 };
			setAttributes({ outcomes: [ ...outcomes, newItem ] });
		};

		const removeOutcome = (index) => {
			const updated = [ ...outcomes ];
			updated.splice(index, 1);
			setAttributes({ outcomes: updated });
		};

		const updateOutcome = (index, key, val) => {
			const updated = [ ...outcomes ];
			updated[index] = { ...updated[index], [key]: val };
			setAttributes({ outcomes: updated });
		};
		useEffect(() => {
			if (outcomes.length === 0) {
				addOutcome();
			}
		}, []);
		useEffect(() => {
			// Only run migration if we have legacy data and no outcomes yet
			if (outcomes.length === 0 && (percentageValues.length > 0 || textOutcomes.length > 0 || outcome)) {
				const newOutcomes = [];
				if (percentageValues.length > 0) {
					percentageValues.forEach(item => {
						if (item.enabled) {
							newOutcomes.push({
								text: item.outcome || '',
								showPercentage: true,
								percentage: item.value || 0
							});
						}
					});
				}
				if (textOutcomes.length > 0) {
					textOutcomes.forEach(item => {
						newOutcomes.push({
							text: item.text || '',
							showPercentage: false,
							percentage: 0
						});
					});
				}
				if (outcome && newOutcomes.length === 0) {
					newOutcomes.push({
						text: outcome,
						showPercentage: false,
						percentage: 0
					});
				}
				if (newOutcomes.length > 0) {
					setAttributes({
						outcomes: newOutcomes,
					});
				}
			}
		}, []);

		return (
			<Fragment>
				<div {...blockProps}>
					<EditorContainer blockTitle="Case Study: Outcomes">
					<BlockSettings
						title={title}
						onTitleChange={(val) => setAttributes({ title: val })}
						description={description}
						onDescriptionChange={(val) => setAttributes({ description: val })}
						icon={icon}
						onIconSelect={(url) => setAttributes({ icon: url })}
						onIconRemove={() => setAttributes({ icon: '' })}
						accentColor={accentColor}
						onAccentColorChange={(color) => setAttributes({ accentColor: color })}
					/>
					<div className="bg-neutral-20 rounded-xl p-6 mb-4">
						<Panel className="border-0 shadow-none">
							<PanelBody
								title="Outcomes"
								initialOpen={true}
							>
								<p className="text-sm text-neutral-60 mb-4">Add outcomes with optional percentage indicators.</p>

								{ outcomes.map((item, i) => (
									<div key={ i } className="mb-6 p-4 bg-white rounded-lg border border-neutral-30">
										<div className="flex justify-between items-center mb-3 pb-2 border-b border-neutral-20">
											<h4 className="text-base font-medium">Outcome {i + 1}</h4>
											<ToggleControl
												label="Show Percentage"
												checked={ item.showPercentage }
												onChange={ (val) => updateOutcome(i, 'showPercentage', val) }
											/>
										</div>

										<div className="mb-4">
											<TextControl
												label="Outcome Text"
												value={item.text}
												onChange={(val) => updateOutcome(i, 'text', val)}
												placeholder={`Enter outcome text ${i + 1}`}
												tagName="p"
												description="This text will appear as the outcome."
											/>
										</div>

										{ item.showPercentage && (
											<div className="mb-4">
												<TextControl
													label="Percentage Value"
													value={item.percentage.toString()}
													onChange={(val) => updateOutcome(i, 'percentage', parseInt(val) || 0)}
													placeholder="Enter percentage value"
													tagName="p"
													description="Enter any percentage value. The circular indicator will fill completely at 100% (values above 100% will still show as a full circle)."
												/>
											</div>
										)}

										<Button
											isDestructive
											className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
											onClick={ () => removeOutcome(i) }
											icon="trash"
										>
											Remove Outcome
										</Button>
									</div>
								)) }

								<div className="mt-4">
									<Button
										onClick={ addOutcome }
										className="w-full justify-center border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 py-2 rounded-md transition-all"
										icon="plus"
									>
										Add Outcome
									</Button>
								</div>
							</PanelBody>
						</Panel>
					</div>
					{ outcome && (
						<div className="bg-neutral-20 rounded-xl p-6 mb-4">
							<Panel className="border-0 shadow-none">
								<PanelBody
									title="Legacy Data (Will be migrated)"
									initialOpen={false}
								>
									<p className="text-sm text-neutral-60 mb-4">This data will be automatically migrated to the new format.</p>
									<TextControl
										label="Legacy Outcome Text"
										value={outcome}
										onChange={(val) => setAttributes({ outcome: val })}
										placeholder="Enter outcome text"
										tagName="p"
										description="This field is for backward compatibility."
									/>
								</PanelBody>
							</Panel>
						</div>
					) }
					</EditorContainer>
				</div>
			</Fragment>
		);
	},
	save: ({ attributes }) => {
		const blockProps = useBlockProps.save();
		const {
			title,
			description,
			icon,
			accentColor,
			outcome,
			percentageValues,
			textOutcomes,
			outcomes,
		} = attributes;

		return (
			<div {...blockProps}>
				<div style={{ display: 'none' }} data-accent-color={ accentColor }>
					<RichText.Content tagName="h2" value={ title } />
					<RichText.Content tagName="p" value={ description } />
					{ icon && <img src={ icon } alt="Icon" /> }
					{ outcomes && outcomes.map((item, i) => (
						<div key={ `outcome-${ i }` }>
							<span>{ item.showPercentage ? 'true' : 'false' }</span>
							{ item.showPercentage && <span>{ item.percentage }</span> }
							<RichText.Content tagName="p" value={ item.text } />
						</div>
					)) }
					{ percentageValues && percentageValues.map((item, i) => (
						<div key={ `percentage-${ i }` }>
							<span>{ item.enabled ? 'true' : 'false' }</span>
							<span>{ item.value }</span>
							<RichText.Content tagName="p" value={ item.outcome } />
						</div>
					)) }
					{ textOutcomes && textOutcomes.map((item, i) => (
						<div key={ `text-outcome-${ i }` }>
							<RichText.Content tagName="p" value={ item.text } />
						</div>
					)) }
					<RichText.Content tagName="p" value={ outcome } />
				</div>
				<div data-dynamic="sage/case-study-outcomes"></div>
			</div>
		);
	},
});
