import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import { Panel, PanelBody } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

const DEFAULT_STEP = {
    tagline: '',
    title: '',
    description: '',
};

const TAGLINE_COLORS = [
    '#E66953',
    '#33A5DC',
    '#E65975',
    '#6EB186',
];

registerBlockType('sage/steps', {
    apiVersion: 2,
    title: 'Steps',
    icon: 'editor-ol',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        backgroundImage: {
            type: 'string',
            default: '',
        },
        steps: {
            type: 'array',
            default: [
                { ...DEFAULT_STEP }
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, backgroundImage, steps } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const addStep = () => {
            const newSteps = [...steps, { ...DEFAULT_STEP }];
            setAttributes({ steps: newSteps });
            setActiveTab(newSteps.length - 1);
        };

        const removeStep = (index) => {
            const newSteps = [...steps];
            newSteps.splice(index, 1);
            setAttributes({ steps: newSteps });
            setActiveTab(Math.min(activeTab, newSteps.length - 1));
        };

        const handleStepChange = (index, field, value) => {
            const updatedSteps = [...steps];
            updatedSteps[index] = { ...updatedSteps[index], [field]: value };
            setAttributes({ steps: updatedSteps });
        };

        const getTaglineColor = (index) => {
            return TAGLINE_COLORS[index % TAGLINE_COLORS.length];
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Steps Process">
                <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                    <Panel className="border-0 shadow-none">
                        <PanelBody title="Block Settings" initialOpen={true}>
                            <TextControl
                                label="Title"
                                value={title}
                                onChange={(val) => setAttributes({ title: val })}
                                placeholder="Enter title"
                                tagName="h2"
                            />

                            <TextControl
                                label="Description"
                                value={description}
                                onChange={(val) => setAttributes({ description: val })}
                                placeholder="Enter description"
                                tagName="p"
                                multiline={true}
                            />

                            <ImageUploader
                                image={backgroundImage}
                                onSelect={(url) => setAttributes({ backgroundImage: url })}
                                onRemove={() => setAttributes({ backgroundImage: '' })}
                                label="Background Image"
                                description="Upload a background image for the steps block."
                                height={48}
                                objectFit="cover"
                                altText="Background"
                            />
                        </PanelBody>
                    </Panel>
                </div>

                <TabNavigation
                    items={steps}
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    onAddItem={addStep}
                    onRemoveItem={() => removeStep(activeTab)}
                    getItemTitle={(item, index) => item.title || `Step ${index + 1}`}
                    itemName="Step"
                    addButtonTitle="Add New Step"
                />

                {steps.length > 0 && (
                    <ItemEditor
                        itemIndex={activeTab}
                        onRemove={() => removeStep(activeTab)}
                        itemName="Step"
                        showRemoveButton={steps.length > 1}
                    >
                        <div className="mb-4">
                            <TextControl
                                label={`Step Tagline (Color: ${getTaglineColor(activeTab)})`}
                                value={steps[activeTab].tagline}
                                onChange={(value) => handleStepChange(activeTab, 'tagline', value)}
                                placeholder="Enter step tagline (e.g., STEP 1)"
                                tagName="h5"
                            />
                        </div>

                        <div className="mb-4">
                            <TextControl
                                label="Step Title"
                                value={steps[activeTab].title}
                                onChange={(value) => handleStepChange(activeTab, 'title', value)}
                                placeholder="Enter step title"
                                tagName="h3"
                            />
                        </div>

                        <div className="mb-4">
                            <TextControl
                                label="Step Description"
                                value={steps[activeTab].description}
                                onChange={(value) => handleStepChange(activeTab, 'description', value)}
                                placeholder="Enter step description"
                                tagName="p"
                                multiline={true}
                            />
                        </div>
                    </ItemEditor>
                )}
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
