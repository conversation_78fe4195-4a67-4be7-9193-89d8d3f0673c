import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps
} from '@wordpress/block-editor';
import {
    Button,
    Panel,
    PanelBody
} from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/partnership-models', {
    apiVersion: 2,
    title: 'Partnership Models',
    icon: 'groups',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        models: {
            type: 'array',
            default: [
                {
                    tagline: '',
                    title: '',
                    description: '',
                    benefits: [
                        {
                            image: '',
                            text: ''
                        }
                    ]
                }
            ],
        }
    },
    edit: ({ attributes, setAttributes }) => {
        const { models } = attributes;
        const [activeModel, setActiveModel] = useState(0);
        const [activeBenefit, setActiveBenefit] = useState(0);

        const handleModelChange = (index, field, value) => {
            const newModels = [...models];
            newModels[index][field] = value;
            setAttributes({ models: newModels });
        };

        const addModel = () => {
            const newModels = [
                ...models,
                {
                    tagline: '',
                    title: '',
                    description: '',
                    benefits: [
                        {
                            image: '',
                            text: ''
                        }
                    ]
                }
            ];
            setAttributes({ models: newModels });
            setActiveModel(newModels.length - 1);
            setActiveBenefit(0);
        };

        const removeModel = (index) => {
            if (models.length <= 1) {
                return;
            }

            const newModels = [...models];
            newModels.splice(index, 1);
            setAttributes({ models: newModels });
            setActiveModel(Math.max(activeModel - 1, 0));
            setActiveBenefit(0);
        };

        const handleBenefitChange = (modelIndex, benefitIndex, field, value) => {
            const newModels = [...models];
            newModels[modelIndex].benefits[benefitIndex][field] = value;
            setAttributes({ models: newModels });
        };

        const addBenefit = (modelIndex) => {
            const newModels = [...models];
            newModels[modelIndex].benefits = [
                ...newModels[modelIndex].benefits,
                {
                    image: '',
                    text: ''
                }
            ];
            setAttributes({ models: newModels });
            setActiveBenefit(newModels[modelIndex].benefits.length - 1);
        };

        const removeBenefit = (modelIndex, benefitIndex) => {
            if (models[modelIndex].benefits.length <= 1) {
                return;
            }

            const newModels = [...models];
            newModels[modelIndex].benefits.splice(benefitIndex, 1);
            setAttributes({ models: newModels });
            setActiveBenefit(Math.max(activeBenefit - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Partnership Models</h2>
                        </div>

                        <div className="mb-6 mt-6">
                            <TabNavigation
                                items={models}
                                activeTab={activeModel}
                                onTabChange={(index) => {
                                    setActiveModel(index);
                                    setActiveBenefit(0);
                                }}
                                onAddItem={addModel}
                                onRemoveItem={() => removeModel(activeModel)}
                                getItemTitle={(model, index) => model.title && model.title.trim().length > 0 ? model.title : `Model ${index + 1}`}
                                itemName="Model"
                                addButtonTitle="Add New Model"
                            />

                            {models.length > 0 && (
                                <ItemEditor
                                    itemIndex={activeModel}
                                    onRemove={() => removeModel(activeModel)}
                                    itemName="Partnership Model"
                                    showRemoveButton={models.length > 1}
                                >
                                    <TextControl
                                        label="Tagline"
                                        value={models[activeModel].tagline}
                                        onChange={(value) => handleModelChange(activeModel, 'tagline', value)}
                                        placeholder="Enter model tagline"
                                        tagName="p"
                                    />

                                    <TextControl
                                        label="Title"
                                        value={models[activeModel].title}
                                        onChange={(value) => handleModelChange(activeModel, 'title', value)}
                                        placeholder="Enter model title"
                                        tagName="h3"
                                    />

                                    <TextControl
                                        label="Description"
                                        value={models[activeModel].description}
                                        onChange={(value) => handleModelChange(activeModel, 'description', value)}
                                        placeholder="Enter model description"
                                        tagName="div"
                                        multiline={true}
                                    />

                                    <div className="mb-6 mt-6">
                                        <div className="mb-4 border-b border-neutral-30 pb-2">
                                            <h3 className="text-base font-medium text-neutral-90">Benefits</h3>
                                        </div>

                                        <TabNavigation
                                            items={models[activeModel].benefits}
                                            activeTab={activeBenefit}
                                            onTabChange={setActiveBenefit}
                                            onAddItem={() => addBenefit(activeModel)}
                                            onRemoveItem={() => removeBenefit(activeModel, activeBenefit)}
                                            getItemTitle={(benefit, index) => benefit.text && benefit.text.trim().length > 0 ? benefit.text.substring(0, 20) + (benefit.text.length > 20 ? '...' : '') : `Benefit ${index + 1}`}
                                            itemName="Benefit"
                                            addButtonTitle="Add New Benefit"
                                        />

                                        {models[activeModel].benefits.length > 0 && (
                                            <ItemEditor
                                                itemIndex={activeBenefit}
                                                onRemove={() => removeBenefit(activeModel, activeBenefit)}
                                                itemName="Benefit"
                                                showRemoveButton={models[activeModel].benefits.length > 1}
                                            >
                                                <ImageUploader
                                                    image={models[activeModel].benefits[activeBenefit].image}
                                                    onSelect={(url) => handleBenefitChange(activeModel, activeBenefit, 'image', url)}
                                                    onRemove={() => handleBenefitChange(activeModel, activeBenefit, 'image', '')}
                                                    label="Benefit Icon"
                                                    description="Upload an icon for this benefit."
                                                    height={48}
                                                    objectFit="contain"
                                                    altText="Benefit Icon"
                                                />

                                                <TextControl
                                                    label="Benefit Text"
                                                    value={models[activeModel].benefits[activeBenefit].text}
                                                    onChange={(value) => handleBenefitChange(activeModel, activeBenefit, 'text', value)}
                                                    placeholder="Enter benefit text"
                                                    tagName="p"
                                                    multiline={true}
                                                />
                                            </ItemEditor>
                                        )}
                                    </div>
                                </ItemEditor>
                            )}
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { models } = attributes;

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    {models?.map((model, index) => (
                        <div key={`model-${index}`}>
                            <RichText.Content tagName="p" value={model.tagline} />
                            <RichText.Content tagName="h3" value={model.title} />
                            <RichText.Content tagName="div" value={model.description} />

                            {model.benefits?.map((benefit, benefitIndex) => (
                                <div key={`benefit-${index}-${benefitIndex}`}>
                                    <img src={benefit.image} alt="" style={{ display: 'none' }} />
                                    <RichText.Content tagName="p" value={benefit.text} />
                                </div>
                            ))}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/partnership-models"></div>
            </div>
        );
    }
});