import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps
} from '@wordpress/block-editor';
import {
    SelectControl,
    Panel,
    PanelBody
} from '@wordpress/components';
import {
    SectionHeader,
    ButtonEditor,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    TextControl
} from './editor';
import { Fragment, useState, useEffect } from '@wordpress/element';
import { commonAttributes } from './common';

registerBlockType('sage/grid', {
    apiVersion: 2,
    title: 'Grid',
    icon: 'editor-table',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        variant: {
            type: 'string',
            default: 'default'
        },
        items: {
            type: 'array',
            default: [
                {
                    title: '',
                    subtitle: '',
                    tagline: '',
                    description: '',
                }
            ],
        },
        simpleItems: {
            type: 'array',
            default: []
        }
    },
    deprecated: [
        {
            attributes: {
                ...commonAttributes,
                items: {
                    type: 'array',
                    default: [
                        {
                            title: '',
                            subtitle: '',
                            tagline: '',
                            description: '',
                        }
                    ],
                }
            },
            save: ({ attributes }) => {
                const blockProps = useBlockProps.save();

                return (
                    <div {...blockProps}>
                        <div style={{ display: 'none' }}>
                            <RichText.Content tagName="h2" value={attributes.title} />
                            <RichText.Content tagName="span" value={attributes.buttonText} />

                            {attributes.items?.map((item, index) => (
                                <div key={`grid-item-${index}`}>
                                    <RichText.Content tagName="h3" value={item.title} />
                                    <RichText.Content tagName="h4" value={item.subtitle} />
                                    <RichText.Content tagName="span" value={item.tagline} />
                                    <RichText.Content tagName="p" value={item.description} />
                                </div>
                            ))}
                        </div>
                        <div data-dynamic="sage/grid"></div>
                    </div>
                );
            },
            migrate: (attributes) => {
                return {
                    ...attributes,
                    variant: 'default',
                    simpleItems: []
                };
            }
        }
    ],
    edit: ({ attributes, setAttributes }) => {
        const { title, buttonText, buttonLink, items, variant, simpleItems } = attributes;
        const [activeTab, setActiveTab] = useState(0);
        const [simpleActiveTab, setSimpleActiveTab] = useState(0);
        useEffect(() => {
            if (variant === 'simple' && (!simpleItems || simpleItems.length === 0)) {
                setAttributes({
                    simpleItems: [
                        {
                            image: '',
                            title: '',
                            description: ''
                        }
                    ]
                });
            }
        }, [variant, simpleItems, setAttributes]);
        const handleItemChange = (index, field, value) => {
            const newItems = [...items];
            newItems[index][field] = value;
            setAttributes({ items: newItems });
        };

        const addItem = () => {
            const newItems = [
                ...items,
                {
                    title: '',
                    subtitle: '',
                    tagline: '',
                    description: '',
                },
            ];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeItem = (index) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ items: newItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };
        const handleSimpleItemChange = (index, field, value) => {
            const newItems = [...simpleItems];
            newItems[index][field] = value;
            setAttributes({ simpleItems: newItems });
        };

        const addSimpleItem = () => {
            const newItems = [
                ...(simpleItems || []),
                {
                    image: '',
                    title: '',
                    description: ''
                },
            ];
            setAttributes({ simpleItems: newItems });
            setSimpleActiveTab(newItems.length - 1);
        };

        const removeSimpleItem = (index) => {
            if (simpleItems.length <= 1) {
                return;
            }

            const newItems = [...simpleItems];
            newItems.splice(index, 1);
            setAttributes({ simpleItems: newItems });
            setSimpleActiveTab(Math.max(simpleActiveTab - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Grid</h2>
                        </div>
                    <SectionHeader
                        title="Grid Settings"
                        description="Select the type of grid layout you want to display."
                    />
                    <SelectControl
                        label="Grid Variant"
                        value={variant}
                        options={[
                            { label: 'Default', value: 'default' },
                            { label: 'Simple', value: 'simple' }
                        ]}
                        onChange={(newVariant) => setAttributes({ variant: newVariant })}
                        className="mb-6"
                    />

                    {variant === 'default' && (
                        <div>
                            <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Grid Content"
                                        initialOpen={true}
                                    >
                                        <TextControl
                                            label="Grid Title"
                                            value={title}
                                            onChange={(val) => setAttributes({ title: val })}
                                            placeholder="Enter main title"
                                            tagName="h2"
                                        />

                                        <ButtonEditor
                                            buttonText={buttonText}
                                            buttonLink={buttonLink}
                                            onChangeText={(val) => setAttributes({ buttonText: val })}
                                            onChangeLink={(val) => setAttributes({ buttonLink: val })}
                                            label="Grid Button"
                                        />
                                    </PanelBody>
                                </Panel>
                            </div>

                            <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Grid Items"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add items to display in the grid layout.</p>

                                        <TabNavigation
                                            items={items}
                                            activeTab={activeTab}
                                            onTabChange={setActiveTab}
                                            onAddItem={addItem}
                                            onRemoveItem={() => removeItem(activeTab)}
                                            getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                            itemName="Item"
                                            addButtonTitle="Add New Item"
                                        />

                                        {items.length > 0 && (
                                            <ItemEditor
                                                itemIndex={activeTab}
                                                onRemove={() => removeItem(activeTab)}
                                                itemName="Item"
                                                showRemoveButton={items.length > 1}
                                            >

                                                <div className="mt-4">
                                                    <TextControl
                                                        label="Item Title"
                                                        value={items[activeTab].title}
                                                        onChange={(val) => handleItemChange(activeTab, 'title', val)}
                                                        placeholder="Enter item title"
                                                        tagName="h3"
                                                    />

                                                    <TextControl
                                                        label="Item Subtitle"
                                                        value={items[activeTab].subtitle}
                                                        onChange={(val) => handleItemChange(activeTab, 'subtitle', val)}
                                                        placeholder="Enter item subtitle"
                                                        tagName="h4"
                                                    />

                                                    <TextControl
                                                        label="Item Tagline"
                                                        value={items[activeTab].tagline}
                                                        onChange={(val) => handleItemChange(activeTab, 'tagline', val)}
                                                        placeholder="Enter item tagline"
                                                        tagName="span"
                                                    />

                                                    <TextControl
                                                        label="Item Description"
                                                        value={items[activeTab].description}
                                                        onChange={(val) => handleItemChange(activeTab, 'description', val)}
                                                        placeholder="Enter item description"
                                                        tagName="p"
                                                        multiline={true}
                                                    />
                                                </div>

                                            </ItemEditor>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>
                        </div>
                    )}

                    {variant === 'simple' && simpleItems && simpleItems.length > 0 && (
                        <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                            <Panel className="border-0 shadow-none">
                                <PanelBody
                                    title="Simple Grid Items"
                                    initialOpen={true}
                                >
                                    <p className="text-sm text-neutral-60 mb-4">Add items with images to display in the simple grid layout.</p>

                                    <TabNavigation
                                        items={simpleItems}
                                        activeTab={simpleActiveTab}
                                        onTabChange={setSimpleActiveTab}
                                        onAddItem={addSimpleItem}
                                        onRemoveItem={() => removeSimpleItem(simpleActiveTab)}
                                        getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                        itemName="Item"
                                        addButtonTitle="Add New Item"
                                    />

                                    {simpleItems.length > 0 && (
                                        <ItemEditor
                                            itemIndex={simpleActiveTab}
                                            onRemove={() => removeSimpleItem(simpleActiveTab)}
                                            itemName="Item"
                                            showRemoveButton={simpleItems.length > 1}
                                        >

                                            <div className="mt-4">
                                                <ImageUploader
                                                    image={simpleItems[simpleActiveTab].image}
                                                    onSelect={(url) => handleSimpleItemChange(simpleActiveTab, 'image', url)}
                                                    onRemove={() => handleSimpleItemChange(simpleActiveTab, 'image', '')}
                                                    label="Item Image"
                                                    description="Upload an image for this grid item."
                                                    height={48}
                                                    objectFit="cover"
                                                    altText="Item Image"
                                                />
                                            </div>

                                            <TextControl
                                                label="Item Title"
                                                value={simpleItems[simpleActiveTab].title}
                                                onChange={(val) => handleSimpleItemChange(simpleActiveTab, 'title', val)}
                                                placeholder="Enter item title"
                                                tagName="h3"
                                            />

                                            <TextControl
                                                label="Item Description"
                                                value={simpleItems[simpleActiveTab].description}
                                                onChange={(val) => handleSimpleItemChange(simpleActiveTab, 'description', val)}
                                                placeholder="Enter item description"
                                                tagName="p"
                                                multiline={true}
                                            />

                                        </ItemEditor>
                                    )}
                                </PanelBody>
                            </Panel>
                        </div>
                    )}
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { variant, items, simpleItems } = attributes;

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="span" value={attributes.buttonText} />
                    <span data-variant={variant}></span>
                    {variant === 'default' && items?.map((item, index) => (
                        <div key={`grid-item-${index}`}>
                            <RichText.Content tagName="h3" value={item.title} />
                            <RichText.Content tagName="h4" value={item.subtitle} />
                            <RichText.Content tagName="span" value={item.tagline} />
                            <RichText.Content tagName="p" value={item.description} />
                        </div>
                    ))}
                    {variant === 'simple' && simpleItems?.map((item, index) => (
                        <div key={`simple-grid-item-${index}`}>
                            <img src={item.image} alt="" style={{ display: 'none' }} />
                            <RichText.Content tagName="h3" value={item.title} />
                            <RichText.Content tagName="p" value={item.description} />
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/grid"></div>
            </div>
        );
    }
});