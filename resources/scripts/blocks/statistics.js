import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { RangeControl } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/statistics', {
    apiVersion: 2,
    title: 'Statistics',
    icon: 'chart-bar',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        stats: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, stats } = attributes;
        const [activeStat, setActiveStat] = useState(0);

        const addStat = () => {
            const newStat = {
                value: '',
                label: '',
                height: 0,
            };
            const newStats = [...stats, newStat];
            setAttributes({ stats: newStats });
            setActiveStat(newStats.length - 1);
        };

        if (stats.length === 0) {
            addStat();
        }

        const handleStatChange = (index, field, value) => {
            const updatedStats = [...stats];
            updatedStats[index] = { ...updatedStats[index], [field]: value };
            setAttributes({ stats: updatedStats });
        };

        const removeStat = (index) => {
            const updatedStats = [...stats];
            updatedStats.splice(index, 1);
            setAttributes({ stats: updatedStats });
            setActiveStat(Math.min(index, updatedStats.length - 1));
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Statistics">
                    <div className="mb-6">
                        <TextControl
                            label="Block Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />
                    </div>

                    <div className="mt-6">
                        <TabNavigation
                            items={stats}
                            activeTab={activeStat}
                            onTabChange={setActiveStat}
                            onAddItem={addStat}
                            onRemoveItem={() => removeStat(activeStat)}
                            getItemTitle={(item, index) => item.value || `Stat ${index + 1}`}
                            itemName="Stat"
                            addButtonTitle="Add New Stat"
                        />

                        {stats.length > 0 && (
                            <ItemEditor
                                itemIndex={activeStat}
                                onRemove={() => removeStat(activeStat)}
                                itemName="Stat"
                                showRemoveButton={stats.length > 1}
                            >
                                <TextControl
                                    label="Stat Value"
                                    value={stats[activeStat].value}
                                    onChange={(value) => handleStatChange(activeStat, 'value', value)}
                                    placeholder="Enter stat value (e.g., 250+)"
                                    tagName="h3"
                                />

                                <TextControl
                                    label="Stat Label"
                                    value={stats[activeStat].label}
                                    onChange={(value) => handleStatChange(activeStat, 'label', value)}
                                    placeholder="Enter stat label (e.g., Interns)"
                                    tagName="p"
                                />

                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-neutral-70 mb-2">
                                        Stat Height (%)
                                    </label>
                                    <RangeControl
                                        value={stats[activeStat].height}
                                        onChange={(value) => handleStatChange(activeStat, 'height', value)}
                                        min={0}
                                        max={100}
                                        className="mb-2"
                                    />
                                    <p className="mt-2 text-xs text-neutral-60">
                                        Controls the vertical size of the stat box. 0% = minimum height (116px), 100% = maximum height (354px).
                                    </p>
                                </div>
                            </ItemEditor>
                        )}
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
