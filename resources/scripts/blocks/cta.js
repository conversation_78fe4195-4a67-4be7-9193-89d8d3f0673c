import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import { Button, SelectControl } from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/cta', {
    apiVersion: 2,
    title: 'Call to Action (CTA)',
    icon: 'megaphone',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        ctaTitle: {
            type: 'string',
        },
        ctaFile: {
            type: 'string',
        },
        inputPlaceholder: {
            type: 'string',
        },
        taglineColor: {
            type: 'string',
            default: '',
        },
        items: {
            type: 'array',
            default: [
                {
                    image: '',
                    title: '',
                    description: '',
                },
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, subtitle, ctaTitle, ctaFile, inputPlaceholder, taglineColor, items } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleItemChange = (index, field, value) => {
            const updatedItems = [...items];
            updatedItems[index][field] = value;
            setAttributes({ items: updatedItems });
        };

        const addItem = () => {
            const newItems = [...items, { image: '', title: '', description: '' }];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeItem = (index) => {
            const updatedItems = [...items];
            updatedItems.splice(index, 1);
            setAttributes({ items: updatedItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Call to Action (CTA)</h2>
                        </div>

                        <div className="mb-4">
                            <TextControl
                                label="Subtitle"
                                value={subtitle}
                                onChange={(value) => setAttributes({ subtitle: value })}
                                placeholder="Enter subtitle"
                                tagName="h3"
                            />

                            <div className="mt-2">
                                <label className="block text-sm font-medium text-neutral-70 mb-2">Tagline Color</label>
                                <div className="p-3 bg-white rounded-md border border-neutral-30">
                                    <SelectControl
                                        value={taglineColor}
                                        options={[
                                            { label: 'Select a color', value: '' },
                                            { label: 'Blue', value: 'blue' },
                                            { label: 'Green', value: 'green' },
                                            { label: 'Peach', value: 'peach' },
                                            { label: 'Pink', value: 'pink' },
                                        ]}
                                        onChange={(value) => setAttributes({ taglineColor: value })}
                                    />
                                </div>
                                <p className="mt-1 text-xs text-neutral-60">Select a color for the tagline</p>
                            </div>
                        </div>

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />

                        <div className="mb-6 mt-6 p-4 bg-neutral-20 rounded-md">
                            <div className="mb-4 border-b border-neutral-30 pb-2">
                                <h3 className="text-base font-medium text-neutral-90">CTA Form Settings</h3>
                            </div>

                            <TextControl
                                label="CTA Title"
                                value={ctaTitle}
                                onChange={(value) => setAttributes({ ctaTitle: value })}
                                placeholder="Enter CTA title"
                                tagName="h4"
                            />

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-neutral-70 mb-2">Downloadable File</label>
                                <div className="p-3 bg-white rounded-md border border-neutral-30">
                                    <div className="flex items-center justify-between">
                                        <div className="flex-grow truncate">
                                            {ctaFile ? (
                                                <a
                                                    href={ctaFile}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-primary-80 hover:text-primary-90 truncate block"
                                                >
                                                    {ctaFile.split('/').pop()}
                                                </a>
                                            ) : (
                                                <span className="text-neutral-60">No file selected</span>
                                            )}
                                        </div>
                                        <div className="flex">
                                            <Button
                                                variant="secondary"
                                                className="border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 hover:text-primary-80 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                onClick={() => {
                                                    const input = document.createElement('input');
                                                    input.type = 'file';
                                                    input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx';

                                                    input.onchange = (e) => {
                                                        const file = e.target.files[0];
                                                        if (file) {
                                                            setAttributes({ ctaFile: URL.createObjectURL(file) });
                                                        }
                                                    };

                                                    input.click();
                                                }}
                                            >
                                                {ctaFile ? 'Change File' : 'Upload File'}
                                            </Button>
                                            {ctaFile && (
                                                <Button
                                                    variant="secondary"
                                                    className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 px-3 rounded-md transition-all text-sm flex items-center ml-2"
                                                    onClick={() => setAttributes({ ctaFile: '' })}
                                                >
                                                    Remove
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <p className="mt-1 text-xs text-neutral-60">Upload a file for users to download</p>
                            </div>

                            <TextControl
                                label="Input Placeholder"
                                value={inputPlaceholder}
                                onChange={(value) => setAttributes({ inputPlaceholder: value })}
                                placeholder="Enter input placeholder"
                                tagName="p"
                            />
                        </div>

                        <div className="mb-6">
                            <TabNavigation
                                items={items}
                                activeTab={activeTab}
                                onTabChange={setActiveTab}
                                onAddItem={addItem}
                                onRemoveItem={() => removeItem(activeTab)}
                                getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                itemName="Item"
                                addButtonTitle="Add New Item"
                            />

                            {items.length > 0 && (
                                <ItemEditor
                                    itemIndex={activeTab}
                                    onRemove={() => removeItem(activeTab)}
                                    itemName="Item"
                                    showRemoveButton={items.length > 1}
                                >
                                    <ImageUploader
                                        image={items[activeTab].image}
                                        onSelect={(url) => handleItemChange(activeTab, 'image', url)}
                                        onRemove={() => handleItemChange(activeTab, 'image', '')}
                                        label="Item Image"
                                        description="Upload an image for this item."
                                        height={48}
                                        objectFit="cover"
                                        altText="Item Image"
                                    />

                                    <TextControl
                                        label="Item Title"
                                        value={items[activeTab].title}
                                        onChange={(value) => handleItemChange(activeTab, 'title', value)}
                                        placeholder="Enter item title"
                                        tagName="h4"
                                    />

                                    <TextControl
                                        label="Item Description"
                                        value={items[activeTab].description}
                                        onChange={(value) => handleItemChange(activeTab, 'description', value)}
                                        placeholder="Enter item description"
                                        tagName="p"
                                        multiline={true}
                                    />
                                </ItemEditor>
                            )}
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h3" value={attributes.subtitle} />
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="h4" value={attributes.ctaTitle} />
                    <RichText.Content tagName="p" value={attributes.inputPlaceholder} />

                    {attributes.ctaFile && (
                        <p>
                            <a href={attributes.ctaFile} target="_blank" rel="noopener noreferrer">
                                Download File
                            </a>
                        </p>
                    )}

                    {attributes.items?.map((item, index) => (
                        <div key={`cta-item-${index}`}>
                            <RichText.Content tagName="h4" value={item.title} />
                            <RichText.Content tagName="p" value={item.description} />
                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={item.title ? `${item.title} - CTA Image` : "CTA Image"}
                                />
                            )}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/cta"></div>
            </div>
        );
    }
});
