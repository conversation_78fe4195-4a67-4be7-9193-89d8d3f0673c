<?php
/**
 * Theme Settings Entry Point
 *
 * Backward compatibility wrapper that loads the modularized settings system from
 * functions/settings/ folder. The settings system includes admin menu registration
 * (admin-menu.php), custom post type registration (cpt-registration.php), and
 * individual settings pages for technology icons, case study icons, contact forms,
 * image optimization, and positions management. All settings pages are located in
 * functions/settings/pages/ and provide admin interfaces for managing theme data.
 */

require_once __DIR__ . '/settings/index.php';

